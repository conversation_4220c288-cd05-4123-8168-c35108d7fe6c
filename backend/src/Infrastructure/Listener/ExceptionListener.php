<?php

declare(strict_types=1);

namespace App\Infrastructure\Listener;

use App\Infrastructure\Response\ApiResponseContent;
use App\Infrastructure\Security\Authentication\AuthenticationException;
use App\Infrastructure\Validator\ValidatorException;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Event\ExceptionEvent;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\HttpKernel\KernelEvents;
use Symfony\Component\HttpKernel\Log\Logger;
use Symfony\Component\Routing\Exception\MethodNotAllowedException;
use Symfony\Component\Routing\Exception\RouteNotFoundException;

class ExceptionListener implements EventSubscriberInterface
{
    public const array LOGGABLE_CODES = [
        Response::HTTP_INTERNAL_SERVER_ERROR,
    ];

    public function __construct(private readonly Logger $logger)
    {
    }

    public static function getSubscribedEvents(): array
    {
        return [
            KernelEvents::EXCEPTION => ['onKernelException', 0],
        ];
    }

    public function onKernelException(ExceptionEvent $event): void
    {
        $exception = $event->getThrowable();

        $responseCode = match (\get_class($exception)) {
            RouteNotFoundException::class,
            NotFoundHttpException::class => Response::HTTP_NOT_FOUND,
            ValidatorException::class => Response::HTTP_BAD_REQUEST,
            AuthenticationException::class => Response::HTTP_UNAUTHORIZED,
            default => Response::HTTP_INTERNAL_SERVER_ERROR,
        };

        $message = $exception->getMessage();

        if ($exception->getPrevious() instanceof MethodNotAllowedException) {
            $allowedMethods = $exception->getPrevious()->getAllowedMethods();

            if (1 === \count($allowedMethods) && 'OPTIONS' === $allowedMethods[0]) {
                $responseCode = Response::HTTP_NOT_FOUND;
                $message = 'No route found for "' . $event->getRequest()->getMethod() . ' ' . $event->getRequest()->getPathInfo() . '"';
            }
        }

        $responseContent = ApiResponseContent::createFromMessage($message);

        if ($exception instanceof ValidatorException) {
            $violations = [];
            foreach ($exception->getViolations() as $violation) {
                $violations[$violation->getPropertyPath()] = $violation->getMessage();
            }
            $responseContent->setMetadata(['violations' => $violations]);
        }

        if (\in_array($responseCode, self::LOGGABLE_CODES, true)) {
            $this->logger->error($message, ['exception' => $exception]);
        }

        $event->setResponse(
            new JsonResponse(
                $responseContent->toArray(),
                $responseCode
            )
        );
    }
}

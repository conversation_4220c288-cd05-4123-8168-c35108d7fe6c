<?php

declare(strict_types=1);

namespace App\Infrastructure\Controller;

use App\Domain\Shared\Exception\InfrastructureException;
use App\Infrastructure\Security\Authentication\AuthenticationException;
use App\Infrastructure\Security\Authentication\TokenRefreshService;
use App\Infrastructure\Validator\TokenRefreshValidator;
use App\Infrastructure\Validator\ValidatorException;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

class TokenRefreshController extends AbstractController
{
    public function __construct(
        private readonly TokenRefreshService $tokenRefreshService,
    ) {
    }

    /**
     * @throws \DateMalformedStringException
     * @throws InfrastructureException
     */
    public function __invoke(Request $request): JsonResponse
    {
        $payload = json_decode($request->getContent(), true) ?? [];

        try {
            TokenRefreshValidator::validateRefreshRequest($payload);

            $tokens = $this->tokenRefreshService->refreshToken($payload['refresh_token']);

            return new JsonResponse($tokens, Response::HTTP_OK);
        } catch (ValidatorException $e) {
            return new JsonResponse(['error' => 'Invalid request format'], Response::HTTP_BAD_REQUEST);
        } catch (AuthenticationException $e) {
            return new JsonResponse(['error' => $e->getMessage()], Response::HTTP_UNAUTHORIZED);
        }
    }
}

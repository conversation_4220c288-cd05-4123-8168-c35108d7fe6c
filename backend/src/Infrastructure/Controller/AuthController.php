<?php

declare(strict_types=1);

namespace App\Infrastructure\Controller;

use App\Infrastructure\Security\Authentication\AuthenticationException;
use App\Infrastructure\Security\Authentication\AuthenticationService;
use App\Infrastructure\Validator\UserLoginValidator;
use App\Infrastructure\Validator\ValidatorException;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

class AuthController extends AbstractController
{
    public function __construct(
        private readonly AuthenticationService $authService,
    ) {
    }

    /**
     * @throws \DateMalformedStringException
     */
    public function __invoke(Request $request): JsonResponse
    {
        $payload = json_decode($request->getContent(), true) ?? [];

        try {
            UserLoginValidator::validateLoginRequest($payload);

            $tokens = $this->authService->authenticate($payload['email'], $payload['password']);

            return new JsonResponse($tokens, Response::HTTP_OK);
        } catch (ValidatorException $e) {
            return new JsonResponse(['error' => 'Invalid credentials format'], Response::HTTP_BAD_REQUEST);
        } catch (AuthenticationException $e) {
            return new JsonResponse(['error' => $e->getMessage()], Response::HTTP_UNAUTHORIZED);
        }
    }
}

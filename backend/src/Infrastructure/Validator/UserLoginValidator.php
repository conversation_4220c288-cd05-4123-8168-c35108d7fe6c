<?php

declare(strict_types=1);

namespace App\Infrastructure\Validator;

use Symfony\Component\Validator\Constraints\Collection;
use Symfony\Component\Validator\Constraints\Email;
use Symfony\Component\Validator\Constraints\NotBlank;
use Symfony\Component\Validator\Constraints\Type;

class UserLoginValidator extends CommonValidator
{
    /**
     * @throws ValidatorException
     */
    public static function validateLoginRequest(array $data): void
    {
        $constraints = new Collection([
            'email' => [
                new NotBlank(),
                new Type('string'),
                new Email(),
            ],
            'password' => [
                new NotBlank(),
                new Type('string'),
            ],
        ]);

        self::validate($data, $constraints);
    }
}

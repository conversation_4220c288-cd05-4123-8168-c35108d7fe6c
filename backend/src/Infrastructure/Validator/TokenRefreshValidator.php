<?php

declare(strict_types=1);

namespace App\Infrastructure\Validator;

use Symfony\Component\Validator\Constraints\Collection;
use Symfony\Component\Validator\Constraints\NotBlank;
use Symfony\Component\Validator\Constraints\Type;

class TokenRefreshValidator extends CommonValidator
{
    /**
     * @throws ValidatorException
     */
    public static function validateRefreshRequest(array $data): void
    {
        $constraints = new Collection([
            'refresh_token' => [
                new NotBlank(),
                new Type('string'),
            ],
        ]);

        self::validate($data, $constraints);
    }
}
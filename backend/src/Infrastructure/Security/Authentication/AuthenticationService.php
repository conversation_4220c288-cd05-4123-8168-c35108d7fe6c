<?php

declare(strict_types=1);

namespace App\Infrastructure\Security\Authentication;

use App\Domain\Shared\Email\Email;
use App\Domain\User\User;
use App\Domain\User\UserCriteria;
use App\Domain\User\UserRepository;
use App\Infrastructure\Security\Token\KeyProvider;

readonly class AuthenticationService
{
    public function __construct(
        private UserRepository $userRepository,
        private KeyProvider $keyProvider,
        private string $tokenExpiration = '+1 hour',
        private string $refreshTokenExpiration = '+30 days',
    ) {
    }

    /**
     * @throws AuthenticationException
     * @throws \DateMalformedStringException
     */
    public function authenticate(string $email, string $password): array
    {
        try {
            $user = $this->userRepository->findOneBy(
                UserCriteria::createEmpty()->filterByEmail(new Email($email))
            );
        } catch (\Exception $e) {
            throw new AuthenticationException('Invalid credentials');
        }

        if (!password_verify($password, $user->getPasswordHash())) {
            throw new AuthenticationException('Invalid credentials');
        }

        return [
            'token' => $this->generateAccessToken($user),
            'refresh_token' => $this->generateRefreshToken($user),
        ];
    }

    /**
     * @throws \DateMalformedStringException
     */
    private function generateAccessToken(User $user): string
    {
        $now = new \DateTimeImmutable();
        $exp = $now->modify($this->tokenExpiration);

        $payload = [
            'sub' => $user->getId()->value(),
            'email' => $user->getEmail()->value(),
            'iat' => $now->getTimestamp(),
            'exp' => $exp->getTimestamp(),
            'iss' => 'app_auth',
            'type' => 'access',
        ];

        return $this->keyProvider->createToken($payload);
    }

    /**
     * @throws \DateMalformedStringException
     */
    private function generateRefreshToken(User $user): string
    {
        $now = new \DateTimeImmutable();
        $exp = $now->modify($this->refreshTokenExpiration);

        $payload = [
            'sub' => $user->getId()->value(),
            'email' => $user->getEmail()->value(),
            'iat' => $now->getTimestamp(),
            'exp' => $exp->getTimestamp(),
            'iss' => 'app_auth',
            'type' => 'refresh',
        ];

        return $this->keyProvider->createToken($payload);
    }
}

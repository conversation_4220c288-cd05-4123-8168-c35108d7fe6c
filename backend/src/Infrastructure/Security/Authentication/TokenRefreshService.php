<?php

declare(strict_types=1);

namespace App\Infrastructure\Security\Authentication;

use App\Domain\Shared\Exception\InfrastructureException;
use App\Domain\User\Exception\UserNotFoundException;
use App\Domain\Security\User;
use App\Domain\User\UserCriteria;
use App\Domain\User\UserRepository;
use App\Infrastructure\Security\Token\JwtException;
use App\Infrastructure\Security\Token\JwtManager;
use App\Infrastructure\Security\Token\KeyProvider;

readonly class TokenRefreshService
{
    public function __construct(
        private UserRepository $userRepository,
        private JwtManager $jwtManager,
        private KeyProvider $keyProvider,
        private string $tokenExpiration = '+1 hour',
        private string $refreshTokenExpiration = '+30 days',
    ) {
    }

    /**
     * @throws AuthenticationException
     * @throws \DateMalformedStringException
     * @throws InfrastructureException
     */
    public function refreshToken(string $refreshToken): array
    {
        try {
            $jwt = $this->jwtManager->decode($refreshToken);

            $tokenType = $jwt->getClaimValue('type');
            if ('refresh' !== $tokenType) {
                throw new AuthenticationException('Invalid refresh token');
            }

            $user = $this->jwtManager->getUser($jwt);

            $this->userRepository->findOneBy(
                UserCriteria::createById($user->getId())
            );

            return [
                'token' => $this->generateAccessToken($user),
                'refresh_token' => $this->generateRefreshToken($user),
            ];
        } catch (JwtException|UserNotFoundException $e) {
            throw new AuthenticationException('Invalid refresh token: ' . $e->getMessage());
        }
    }

    /**
     * @throws \DateMalformedStringException
     */
    public function generateAccessToken(User $user): string
    {
        $now = new \DateTimeImmutable();
        $exp = $now->modify($this->tokenExpiration);

        $payload = [
            'sub' => $user->getId()->value(),
            'email' => $user->getEmail()->value(),
            'iat' => $now->getTimestamp(),
            'exp' => $exp->getTimestamp(),
            'iss' => 'app_auth',
            'type' => 'access',
        ];

        return $this->keyProvider->createToken($payload);
    }

    /**
     * @throws \DateMalformedStringException
     */
    public function generateRefreshToken(User $user): string
    {
        $now = new \DateTimeImmutable();
        $exp = $now->modify($this->refreshTokenExpiration);

        $payload = [
            'sub' => $user->getId()->value(),
            'email' => $user->getEmail()->value(),
            'iat' => $now->getTimestamp(),
            'exp' => $exp->getTimestamp(),
            'iss' => 'app_auth',
            'type' => 'refresh',
        ];

        return $this->keyProvider->createToken($payload);
    }
}

<?php

declare(strict_types=1);

namespace App\Infrastructure\Security\Token;

use App\Domain\Security\JwtInterface;
use App\Infrastructure\Security\Token\Claims\AlgorithmClaim;

class KeyProvider
{
    public const string DEFAULT_NAME = 'public-key';

    private string $privateKey;
    private \OpenSSLAsymmetricKey $publicKey;

    public function __construct(
        private readonly string $publicKeyPath,
        private readonly string $privateKeyPath,
    ) {
        $this->loadKeys();
    }

    public function loadKeys(): void
    {
        try {
            // Load private key
            if (is_file($this->privateKeyPath)) {
                $this->privateKey = file_get_contents($this->privateKeyPath);
            }

            // Load public key
            if (is_file($this->publicKeyPath)) {
                $publicKeyContent = file_get_contents($this->publicKeyPath);
                $this->publicKey = openssl_pkey_get_public($publicKeyContent);
            }
        } catch (\Exception $e) {
            // Log error
        }
    }

    /**
     * @throws JwtException
     */
    public function getPublicKey(?string $keyId = null): \OpenSSLAsymmetricKey
    {
        if (empty($this->publicKey)) {
            throw JwtException::keysNotFound();
        }

        return $this->publicKey;
    }

    /**
     * @throws JwtException
     */
    public function verifySignature(JwtInterface $jwt): bool
    {
        try {
            $algorithmClaim = new AlgorithmClaim();
            $alg = $algorithmClaim->getValue($jwt);
        } catch (InvalidClaimException $e) {
            throw JwtException::fromPrevious($e);
        }

        try {
            $pubKey = $this->getPublicKey();

            $ok = openssl_verify(
                $jwt->getEncodedHeader() . '.' . $jwt->getEncodedPayload(),
                $jwt->getSignature(),
                $pubKey,
                AlgorithmClaim::getOpensslAlgorithm($alg)
            );
        } catch (\Exception $e) {
            throw JwtException::fromPrevious($e);
        }

        return 1 === $ok;
    }

    public function createToken(array $payload): string
    {
        $header = [
            'alg' => 'RS256',
            'typ' => 'JWT',
        ];

        $encodedHeader = base64_encode(json_encode($header));
        $encodedPayload = base64_encode(json_encode($payload));

        $signature = '';
        openssl_sign(
            $encodedHeader . '.' . $encodedPayload,
            $signature,
            $this->privateKey,
            OPENSSL_ALGO_SHA256
        );

        $encodedSignature = base64_encode($signature);

        return $encodedHeader . '.' . $encodedPayload . '.' . $encodedSignature;
    }
}

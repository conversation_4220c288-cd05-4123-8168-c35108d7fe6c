<?php

declare(strict_types=1);

namespace App\Infrastructure\Security\Token;

use App\Domain\Security\Claim\ClaimInterface;
use App\Domain\Security\JwtInterface;
use App\Domain\Security\JwtManagerInterface;
use App\Domain\Security\User;
use App\Domain\Shared\Email\Email;
use App\Domain\Shared\Email\InvalidEmailException;
use App\Domain\Shared\Uuid\InvalidUuidException;
use App\Domain\Shared\Uuid\Uuid;
use App\Infrastructure\Security\Token\Claims\AlgorithmClaim;
use App\Infrastructure\Security\Token\Claims\ExpirationClaim;
use App\Infrastructure\Security\Token\Claims\IssuedAtClaim;
use App\Infrastructure\Security\Token\Claims\IssuerClaim;
use App\Infrastructure\Security\Token\Claims\SubjectClaim;

class JwtManager implements JwtManagerInterface
{
    /** @var ClaimInterface[] */
    private array $validatorClaims;

    public function __construct(
        private readonly KeyProvider $keyProvider,
        array $issuers,
        int $timeDriftMs = 0,
    ) {
        $this->validatorClaims = [
            new ExpirationClaim($timeDriftMs),
            new IssuedAtClaim($timeDriftMs),
            new IssuerClaim($issuers),
            new AlgorithmClaim(),
        ];
    }

    public function decode(string $token): JwtInterface
    {
        return new Jwt($token);
    }

    /**
     * @throws JwtException
     */
    public function isValid(JwtInterface $jwt): void
    {
        try {
            foreach ($this->validatorClaims as $claim) {
                $claim->getValue($jwt);
            }
        } catch (InvalidClaimException $e) {
            throw JwtException::fromPrevious($e);
        }

        if (!$this->verifySignature($jwt)) {
            throw JwtException::invalidSignature();
        }
    }

    /**
     * @throws JwtException
     */
    public function verifySignature(JwtInterface $jwt): bool
    {
        return $this->keyProvider->verifySignature($jwt);
    }

    /**
     * @throws JwtException
     */
    public function getUser(string|JwtInterface $token): User
    {
        if ($token instanceof JwtInterface) {
            $jwt = $token;
        } else {
            $jwt = $this->decode($token);
        }

        $this->isValid($jwt);

        try {
            $subject = (new SubjectClaim())->getValue($jwt);
        } catch (InvalidClaimException $e) {
            throw JwtException::fromPrevious($e);
        }

        $email = $jwt->getClaimValue('email');

        try {
            return new User(
                id: new Uuid($subject),
                email: new Email($email),
            );
        } catch (InvalidEmailException|InvalidUuidException $e) {
            throw JwtException::fromPrevious($e);
        }
    }
}

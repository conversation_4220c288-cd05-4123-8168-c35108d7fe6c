<?php

declare(strict_types=1);

namespace App\Infrastructure\Security\Token;

use App\Application\Helper\Base64UrlHelper;
use App\Domain\Security\JwtInterface;

readonly class Jwt implements JwtInterface
{
    private string $encodedHeader;
    private array $header;
    private string $encodedPayload;
    private array $payload;
    private string $encodedSignature;
    private string $signature;

    public function __construct(string $token)
    {
        [$this->encodedHeader, $this->encodedPayload, $this->encodedSignature] = explode('.', $token);

        $this->header = json_decode(Base64UrlHelper::decode($this->encodedHeader), true);
        $this->payload = json_decode(Base64UrlHelper::decode($this->encodedPayload), true);
        $this->signature = Base64UrlHelper::decode($this->encodedSignature);
    }

    public function getEncodedHeader(): string
    {
        return $this->encodedHeader;
    }

    public function getHeader(): array
    {
        return $this->header;
    }

    public function getEncodedPayload(): string
    {
        return $this->encodedPayload;
    }

    public function getPayload(): array
    {
        return $this->payload;
    }

    public function getEncodedSignature(): string
    {
        return $this->encodedSignature;
    }

    public function getSignature(): string
    {
        return $this->signature;
    }

    public function getClaimValue(string $claim): mixed
    {
        return $this->payload[$claim] ?? null;
    }

    public function getHeaderValue(string $name): mixed
    {
        return $this->header[$name] ?? null;
    }
}

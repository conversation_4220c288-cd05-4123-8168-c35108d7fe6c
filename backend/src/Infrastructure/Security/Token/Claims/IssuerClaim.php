<?php

declare(strict_types=1);

namespace App\Infrastructure\Security\Token\Claims;

use App\Domain\Security\Claim\ClaimInterface;
use App\Domain\Security\JwtInterface;
use App\Infrastructure\Security\Token\InvalidClaimException;

final readonly class IssuerClaim implements ClaimInterface
{
    private const string NAME = 'iss';

    public function __construct(private array $issuers)
    {
    }

    /**
     * @throws InvalidClaimException
     */
    public function getValue(JwtInterface $jwt): string
    {
        $value = $jwt->getClaimValue(self::NAME);
        if (empty($value)) {
            throw InvalidClaimException::valueCannotBeEmpty(self::NAME);
        }

        $this->checkClaim($value);

        return $value;
    }

    /**
     * @throws InvalidClaimException
     */
    public function checkClaim(mixed $value): void
    {
        if (!\is_string($value)) {
            throw InvalidClaimException::valueMustBeString(self::NAME);
        }

        if (!\in_array($value, $this->issuers)) {
            throw InvalidClaimException::unknownIssuer($value);
        }
    }

    public function supportedClaim(): string
    {
        return self::NAME;
    }
}

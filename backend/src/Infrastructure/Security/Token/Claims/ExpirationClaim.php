<?php

declare(strict_types=1);

namespace App\Infrastructure\Security\Token\Claims;

use App\Domain\Security\Claim\ClaimInterface;
use App\Domain\Security\JwtInterface;
use App\Infrastructure\Security\Token\InvalidClaimException;

final readonly class ExpirationClaim implements ClaimInterface
{
    private const string NAME = 'exp';

    public function __construct(private int $allowedTimeDrift = 0)
    {
    }

    /**
     * @throws InvalidClaimException
     */
    public function getValue(JwtInterface $jwt): int
    {
        $value = $jwt->getClaimValue(self::NAME);
        if (empty($value)) {
            throw InvalidClaimException::valueCannotBeEmpty(self::NAME);
        }

        $this->checkClaim($value);

        return $value;
    }

    /**
     * @throws InvalidClaimException
     */
    public function checkClaim(mixed $value): void
    {
        if (!\is_float($value) && !\is_int($value)) {
            throw InvalidClaimException::valueMustBeInteger(self::NAME);
        }

        $now = new \DateTimeImmutable();
        $exp = (new \DateTimeImmutable())->setTimestamp($value)->modify("+{$this->allowedTimeDrift} milliseconds");

        if ($now > $exp) {
            throw InvalidClaimException::tokenExpired();
        }
    }

    public function supportedClaim(): string
    {
        return self::NAME;
    }
}

<?php

declare(strict_types=1);

namespace App\Infrastructure\Security\Token\Claims;

use App\Domain\Security\Claim\ClaimInterface;
use App\Domain\Security\Claim\HeaderClaimInterface;
use App\Domain\Security\JwtInterface;

class KeyIdClaim implements ClaimInterface, HeaderClaimInterface
{
    public const string NAME = 'kid'; // Key Id

    public function getValue(JwtInterface $jwt): ?string
    {
        return $jwt->getHeaderValue(self::NAME);
    }

    public function checkClaim(mixed $value): void
    {
    }

    public function supportedClaim(): string
    {
        return self::NAME;
    }

    public function supportedHeader(): string
    {
        return self::NAME;
    }
}

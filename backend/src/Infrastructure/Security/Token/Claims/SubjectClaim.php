<?php

declare(strict_types=1);

namespace App\Infrastructure\Security\Token\Claims;

use App\Domain\Security\Claim\ClaimInterface;
use App\Domain\Security\Claim\UserClaimInterface;
use App\Domain\Security\JwtInterface;
use App\Infrastructure\Security\Token\InvalidClaimException;

final readonly class SubjectClaim implements ClaimInterface, UserClaimInterface
{
    private const string NAME = 'sub';
    public const string FIELD = 'id';

    /**
     * @throws InvalidClaimException
     */
    public function getValue(JwtInterface $jwt): string
    {
        $value = $jwt->getClaimValue(self::NAME);
        if (empty($value)) {
            throw InvalidClaimException::valueCannotBeEmpty(self::NAME);
        }

        $this->checkClaim($value);

        return $value;
    }

    /**
     * @throws InvalidClaimException
     */
    public function checkClaim(mixed $value): void
    {
        if (!\is_string($value)) {
            throw InvalidClaimException::valueMustBeString(self::NAME);
        }
    }

    public function supportedClaim(): string
    {
        return self::NAME;
    }

    public function getSupportedField(): string
    {
        return self::FIELD;
    }
}

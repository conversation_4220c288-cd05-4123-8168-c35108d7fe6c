<?php

declare(strict_types=1);

namespace App\Infrastructure\Security\Token\Claims;

use App\Domain\Security\Claim\ClaimInterface;
use App\Domain\Security\JwtInterface;
use App\Infrastructure\Security\Token\InvalidClaimException;

final readonly class IssuedAtClaim implements ClaimInterface
{
    private const string NAME = 'iat';

    public function __construct(private int $timeDriftMs = 0)
    {
    }

    /**
     * @throws InvalidClaimException
     */
    public function getValue(JwtInterface $jwt): int
    {
        $value = $jwt->getClaimValue(self::NAME);
        if (empty($value)) {
            throw InvalidClaimException::valueCannotBeEmpty(self::NAME);
        }
        $this->checkClaim($value);

        return $value;
    }

    /**
     * @throws InvalidClaimException|\Exception
     */
    public function checkClaim(mixed $value): void
    {
        if (!\is_float($value) && !\is_int($value)) {
            throw InvalidClaimException::valueMustBeInteger(self::NAME);
        }

        $now = new \DateTimeImmutable();
        $iat = (new \DateTimeImmutable())->setTimestamp($value)->modify("-{$this->timeDriftMs} milliseconds");

        if ($now < $iat) {
            throw InvalidClaimException::issuedInFuture();
        }
    }

    public function supportedClaim(): string
    {
        return self::NAME;
    }
}

<?php

declare(strict_types=1);

namespace App\Domain\Security;

use App\Domain\Shared\Email\Email;
use App\Domain\Shared\Entity\EntityWithId;
use App\Domain\Shared\Uuid\Uuid;

class User extends EntityWithId
{
    public function __construct(
        Uuid $id,
        private readonly Email $email,
    ) {
        parent::__construct(id: $id);
    }

    public function getEmail(): Email
    {
        return $this->email;
    }
}

<?php

declare(strict_types=1);

namespace App\Tests\Mother\Domain\Security;

use App\Domain\Security\User;
use App\Domain\Shared\Email\Email;
use App\Domain\Shared\Email\InvalidEmailException;
use App\Domain\Shared\Uuid\InvalidUuidException;
use App\Domain\Shared\Uuid\Uuid;
use App\Tests\Mother\Domain\Email\EmailMother;
use App\Tests\Mother\Domain\Uuid\UuidMother;

class UserMother
{
    /**
     * @throws InvalidUuidException
     * @throws InvalidEmailException
     */
    public static function create(
        ?Uuid $id = null,
        ?Email $email = null,
    ): User {
        return new User(
            id: $id ?? UuidMother::create(),
            email: $email ?? EmailMother::create(),
        );
    }
}

<?php

declare(strict_types=1);

namespace App\Tests\Infrastructure\Security\Authentication;

use App\Domain\Shared\Email\Email;
use App\Domain\User\User;
use App\Domain\User\UserCriteria;
use App\Domain\User\UserRepository;
use App\Infrastructure\Security\Authentication\AuthenticationException;
use App\Infrastructure\Security\Authentication\AuthenticationService;
use App\Infrastructure\Security\Token\KeyProvider;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;

class AuthenticationServiceTest extends TestCase
{
    private AuthenticationService $authService;
    private MockObject $userRepository;
    private MockObject $keyProvider;

    protected function setUp(): void
    {
        $this->userRepository = $this->createMock(UserRepository::class);
        $this->keyProvider = $this->createMock(KeyProvider::class);

        $this->authService = new AuthenticationService(
            $this->userRepository,
            $this->keyProvider
        );
    }

    /**
     * @throws AuthenticationException
     * @throws \DateMalformedStringException
     */
    public function testSuccessfulAuthentication(): void
    {
        // Create a test user
        $email = '<EMAIL>';
        $password = 'password123';
        $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
        
        $user = $this->createMock(User::class);
        $user->method('getPasswordHash')->willReturn($hashedPassword);
        
        // Configure repository to return the user
        $this->userRepository->method('findOneBy')->willReturn($user);
        
        // Configure key provider to create tokens
        $this->keyProvider->method('createToken')
            ->willReturnOnConsecutiveCalls(
                'access.token.signature',
                'refresh.token.signature'
            );
        
        // Authenticate
        $result = $this->authService->authenticate($email, $password);
        
        // Assert result contains both tokens
        $this->assertArrayHasKey('token', $result);
        $this->assertArrayHasKey('refresh_token', $result);
        $this->assertEquals('access.token.signature', $result['token']);
        $this->assertEquals('refresh.token.signature', $result['refresh_token']);
    }

    public function testInvalidCredentials(): void
    {
        // Create a test user with wrong password
        $email = '<EMAIL>';
        $password = 'wrong_password';
        $hashedPassword = password_hash('correct_password', PASSWORD_DEFAULT);
        
        $user = $this->createMock(User::class);
        $user->method('getPasswordHash')->willReturn($hashedPassword);
        
        // Configure repository to return the user
        $this->userRepository->method('findOneBy')->willReturn($user);
        
        // Expect authentication exception
        $this->expectException(AuthenticationException::class);
        $this->expectExceptionMessage('Invalid credentials');
        
        // Authenticate with wrong password
        $this->authService->authenticate($email, $password);
    }

    public function testUserNotFound(): void
    {
        // Configure repository to throw exception
        $this->userRepository->method('findOneBy')
            ->willThrowException(new \Exception('User not found'));
        
        // Expect authentication exception
        $this->expectException(AuthenticationException::class);
        $this->expectExceptionMessage('Invalid credentials');
        
        // Authenticate with non-existent user
        $this->authService->authenticate('<EMAIL>', 'password');
    }
}
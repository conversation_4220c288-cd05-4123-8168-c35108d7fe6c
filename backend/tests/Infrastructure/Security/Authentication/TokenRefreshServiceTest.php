<?php

declare(strict_types=1);

namespace App\Tests\Infrastructure\Security\Authentication;

use App\Domain\Security\JwtInterface;
use App\Domain\Security\User;
use App\Domain\Shared\Email\Email;
use App\Domain\Shared\Email\InvalidEmailException;
use App\Domain\Shared\Uuid\InvalidUuidException;
use App\Domain\Shared\Uuid\Uuid;
use App\Domain\User\UserRepository;
use App\Infrastructure\Security\Authentication\AuthenticationException;
use App\Infrastructure\Security\Authentication\TokenRefreshService;
use App\Infrastructure\Security\Token\JwtException;
use App\Infrastructure\Security\Token\JwtManager;
use App\Infrastructure\Security\Token\KeyProvider;
use App\Tests\Mother\Domain\Security\UserMother as SecurityUserMother;
use App\Tests\Mother\Domain\User\UserMother;
use PHPUnit\Framework\MockObject\Exception;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;

class TokenRefreshServiceTest extends TestCase
{
    private TokenRefreshService $tokenRefreshService;
    private MockObject $userRepository;
    private MockObject $jwtManager;
    private MockObject $keyProvider;
    private MockObject $jwt;
    private User $user;

    /**
     * @throws Exception
     * @throws InvalidEmailException
     * @throws InvalidUuidException
     */
    protected function setUp(): void
    {
        $this->userRepository = $this->createMock(UserRepository::class);
        $this->jwtManager = $this->createMock(JwtManager::class);
        $this->keyProvider = $this->createMock(KeyProvider::class);
        $this->jwt = $this->createMock(JwtInterface::class);

        $this->tokenRefreshService = new TokenRefreshService(
            $this->userRepository,
            $this->jwtManager,
            $this->keyProvider,
            '+1 hour',
            '+30 days'
        );

        // Create a test user
        $userId = new Uuid('3198b62a-3995-4a60-937d-3ffc2391411b');
        $userEmail = new Email('<EMAIL>');
        $this->user = SecurityUserMother::create(
            id: $userId,
            email: $userEmail,
        );
    }

    /**
     * @throws AuthenticationException
     * @throws InvalidEmailException
     * @throws InvalidUuidException
     * @throws \DateMalformedStringException
     */
    public function testSuccessfulTokenRefresh(): void
    {
        // Configure JWT mock to return a refresh token type
        $this->jwt->method('getClaimValue')
            ->willReturnMap([
                ['type', 'refresh'],
                ['sub', '3198b62a-3995-4a60-937d-3ffc2391411b'],
                ['email', '<EMAIL>'],
            ]);

        // Configure JWT manager to decode token and return user
        $this->jwtManager->method('decode')
            ->willReturn($this->jwt);
        $this->jwtManager->method('getUser')
            ->willReturn($this->user);

        // Configure the user repository to return the user
        $this->userRepository->method('findOneBy')
            ->willReturn(
                UserMother::create(
                    id: $this->user->getId(),
                    email: $this->user->getEmail(),
                )
            );

        // Configure a key provider to create tokens
        $this->keyProvider->method('createToken')
            ->willReturnOnConsecutiveCalls(
                'new.access.token',
                'new.refresh.token'
            );

        // Call the refresh token method
        $result = $this->tokenRefreshService->refreshToken('valid.refresh.token');

        // Assert the result contains new tokens
        $this->assertArrayHasKey('token', $result);
        $this->assertArrayHasKey('refresh_token', $result);
        $this->assertEquals('new.access.token', $result['token']);
        $this->assertEquals('new.refresh.token', $result['refresh_token']);
    }

    /**
     * @throws \DateMalformedStringException
     */
    public function testInvalidTokenType(): void
    {
        $this->jwt->method('getClaimValue')
            ->willReturnMap([
                ['type', 'access'], // This should be 'refresh'
            ]);

        $this->jwtManager->method('decode')
            ->willReturn($this->jwt);

        $this->expectException(AuthenticationException::class);
        $this->expectExceptionMessage('Invalid refresh token');

        $this->tokenRefreshService->refreshToken('invalid.token.type');
    }

    public function testJwtExceptionHandling(): void
    {
        // Configure JWT manager to throw exception
        $this->jwtManager->method('decode')
            ->willThrowException(new JwtException('Token expired'));

        // Expect an authentication exception
        $this->expectException(AuthenticationException::class);
        $this->expectExceptionMessage('Invalid refresh token: Token expired');

        // Call the refresh token method
        $this->tokenRefreshService->refreshToken('expired.token');
    }

    /**
     * @throws \DateMalformedStringException
     */
    public function testGenerateAccessToken(): void
    {
        // Configure key provider to create a token
        $this->keyProvider->method('createToken')
            ->willReturn('access.token.signature');

        // Use reflection to access private method
        $method = new \ReflectionMethod(TokenRefreshService::class, 'generateAccessToken');
        $method->setAccessible(true);

        $token = $method->invoke($this->tokenRefreshService, $this->user);

        $this->assertEquals('access.token.signature', $token);
    }

    /**
     * @throws \DateMalformedStringException
     */
    public function testGenerateRefreshToken(): void
    {
        // Configure key provider to create a token
        $this->keyProvider->method('createToken')
            ->willReturn('refresh.token.signature');

        // Use reflection to access private method
        $method = new \ReflectionMethod(TokenRefreshService::class, 'generateRefreshToken');
        $method->setAccessible(true);

        $token = $method->invoke($this->tokenRefreshService, $this->user);

        $this->assertEquals('refresh.token.signature', $token);
    }
}

<?php

declare(strict_types=1);

namespace App\Tests\Infrastructure\Security\Token;

use App\Domain\Shared\Uuid\InvalidUuidException;
use App\Infrastructure\Security\Token\Claims\SubjectClaim;
use App\Infrastructure\Security\Token\JwtException;
use App\Infrastructure\Security\Token\JwtManager;
use App\Infrastructure\Security\Token\KeyProvider;
use App\Tests\Mother\Domain\Uuid\UuidMother;
use PHPUnit\Framework\MockObject\Exception;
use PHPUnit\Framework\TestCase;

class JwtManagerTest extends TestCase
{
    public const string EXAMPLE_TOKEN = 'eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************************************************************************.signature';

    /**
     * @throws \DateMalformedStringException
     */
    private function generateValidToken(
        string $subject = '3198b62a-3995-4a60-937d-3ffc2391411b',
        string $email = '<EMAIL>',
        string $issuer = 'auth_app',
        string $expiration = '+1 hour',
    ): string {
        $now = new \DateTimeImmutable();
        $exp = $now->modify($expiration);

        $header = [
            'alg' => 'RS256',
            'typ' => 'JWT',
        ];

        $payload = [
            'sub' => $subject,
            'email' => $email,
            'iat' => $now->getTimestamp(),
            'exp' => $exp->getTimestamp(),
            'iss' => $issuer,
        ];

        $encodedHeader = base64_encode(json_encode($header));
        $encodedPayload = base64_encode(json_encode($payload));

        // signature verification is mocked
        return $encodedHeader . '.' . $encodedPayload . '.signature';
    }

    /**
     * @throws Exception
     */
    private function getJwtManager(int $timeDrift = 0, bool $verifySignature = false): JwtManager
    {
        $keyProvider = $this->createMock(KeyProvider::class);
        if (!$verifySignature) {
            $keyProvider->method('verifySignature')
                ->willReturn(true);
        }

        return new JwtManager($keyProvider, ['auth_app'], $timeDrift);
    }

    /**
     * @throws Exception
     * @throws \DateMalformedStringException
     */
    public function testSubjectClaim()
    {
        $jwtManager = $this->getJwtManager();
        $jwt = $jwtManager->decode(self::EXAMPLE_TOKEN);
        $subjectClaim = new SubjectClaim();
        $this->assertSame('3198b62a-3995-4a60-937d-3ffc2391411b', $subjectClaim->getValue($jwt));
    }

    /**
     * @throws Exception
     * @throws \DateMalformedStringException
     * @throws JwtException
     */
    public function testIsValid()
    {
        $jwtManager = $this->getJwtManager();

        $token = $this->generateValidToken();
        $jwt = $jwtManager->decode($token);

        // No debería lanzar excepciones
        $jwtManager->isValid($jwt);
        $this->assertTrue(true);
    }

    /**
     * @throws Exception
     * @throws \DateMalformedStringException
     * @throws JwtException
     */
    public function testVerifySignature()
    {
        $jwtManager = $this->getJwtManager();
        // Usar token dinámico con fecha de expiración válida
        $token = $this->generateValidToken();
        $jwt = $jwtManager->decode($token);

        $this->assertTrue($jwtManager->verifySignature($jwt));
    }

    /**
     * @throws Exception
     * @throws \DateMalformedStringException
     * @throws JwtException
     */
    public function testTokenWithCustomExpiration()
    {
        $jwtManager = $this->getJwtManager();

        $token = $this->generateValidToken(expiration: '+2 days');
        $jwt = $jwtManager->decode($token);

        $jwtManager->isValid($jwt);
        $this->assertTrue(true);
    }

    /**
     * @throws Exception
     * @throws \DateMalformedStringException
     */
    public function testExpiredToken()
    {
        $this->expectException(JwtException::class);
        $this->expectExceptionMessage('Token expired');

        $jwtManager = $this->getJwtManager();
        $token = $this->generateValidToken(expiration: '-1 hour');
        $jwt = $jwtManager->decode($token);

        $jwtManager->isValid($jwt);
    }

    /**
     * @throws Exception
     * @throws \DateMalformedStringException
     * @throws JwtException
     * @throws InvalidUuidException
     */
    public function testGetUser()
    {
        $jwtManager = $this->getJwtManager();

        $uuid = UuidMother::create();
        $token = $this->generateValidToken(
            subject: $uuid->value(),
            email: '<EMAIL>'
        );
        $jwt = $jwtManager->decode($token);
        $user = $jwtManager->getUser($jwt);

        $this->assertEquals($uuid, $user->getId());
        $this->assertEquals('<EMAIL>', $user->getEmail()->value());
    }

    /**
     * @throws Exception
     * @throws \DateMalformedStringException
     * @throws JwtException
     */
    public function testGetUserWithInvalidSubject()
    {
        $this->expectException(JwtException::class);
        $this->expectExceptionMessage('Invalid UUID');

        $jwtManager = $this->getJwtManager();

        $token = $this->generateValidToken(
            subject: 'invalid-uuid',
            email: '<EMAIL>'
        );
        $jwt = $jwtManager->decode($token);
        $jwtManager->getUser($jwt);
    }

    /**
     * @throws Exception
     * @throws \DateMalformedStringException
     * @throws JwtException
     * @throws InvalidUuidException
     */
    public function testGetUserWithInvalidEmail()
    {
        $this->expectException(JwtException::class);
        $this->expectExceptionMessage('Invalid email');

        $jwtManager = $this->getJwtManager();

        $token = $this->generateValidToken(
            subject: UuidMother::create()->value(),
            email: 'invalid-email'
        );
        $jwt = $jwtManager->decode($token);
        $jwtManager->getUser($jwt);
    }

    /**
     * @throws Exception
     * @throws \DateMalformedStringException
     * @throws JwtException
     * @throws InvalidUuidException
     */
    public function testGetUserWithInvalidToken()
    {
        $this->expectException(JwtException::class);
        $this->expectExceptionMessage('Token expired');

        $jwtManager = $this->getJwtManager();

        $token = $this->generateValidToken(expiration: '-1 hour');
        $jwt = $jwtManager->decode($token);
        $jwtManager->getUser($jwt);
    }

    /**
     * @throws Exception
     * @throws \DateMalformedStringException
     * @throws JwtException
     */
    public function testGetUserWithInvalidSignature()
    {
        $this->expectException(JwtException::class);
        $this->expectExceptionMessage('Invalid signature');

        $jwtManager = $this->getJwtManager(verifySignature: true);

        $token = $this->generateValidToken();
        $jwt = $jwtManager->decode($token);
        $jwtManager->isValid($jwt);
    }
}

<?php

declare(strict_types=1);

namespace App\Tests\Infrastructure\Validator;

use App\Infrastructure\Validator\TokenRefreshValidator;
use App\Infrastructure\Validator\ValidatorException;
use PHPUnit\Framework\TestCase;

class TokenRefreshValidatorTest extends TestCase
{
    /**
     * @throws ValidatorException
     */
    public function testValidRefreshTokenData(): void
    {
        $data = [
            'refresh_token' => 'valid.refresh.token',
        ];

        TokenRefreshValidator::validateRefreshRequest($data);

        $this->assertTrue(true);
    }

    public function testMissingRefreshToken(): void
    {
        $data = [];

        $this->expectException(ValidatorException::class);
        TokenRefreshValidator::validateRefreshRequest($data);
    }

    public function testEmptyRefreshToken(): void
    {
        $data = [
            'refresh_token' => '',
        ];

        $this->expectException(ValidatorException::class);
        TokenRefreshValidator::validateRefreshRequest($data);
    }

    public function testInvalidTypeRefreshToken(): void
    {
        $data = [
            'refresh_token' => 123, // Should be string
        ];

        $this->expectException(ValidatorException::class);
        TokenRefreshValidator::validateRefreshRequest($data);
    }
}
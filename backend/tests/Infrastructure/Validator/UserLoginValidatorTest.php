<?php

declare(strict_types=1);

namespace App\Tests\Infrastructure\Validator;

use App\Infrastructure\Validator\UserLoginValidator;
use App\Infrastructure\Validator\ValidatorException;
use PHPUnit\Framework\TestCase;

class UserLoginValidatorTest extends TestCase
{
    /**
     * @throws ValidatorException
     */
    public function testValidLoginData(): void
    {
        $data = [
            'email' => '<EMAIL>',
            'password' => 'validpassword',
        ];

        // This should not throw an exception
        UserLoginValidator::validateLoginRequest($data);

        // If we reach this point, the test passes
        $this->assertTrue(true);
    }

    public function testInvalidEmail(): void
    {
        $data = [
            'email' => 'invalid-email',
            'password' => 'validpassword',
        ];

        $this->expectException(ValidatorException::class);
        UserLoginValidator::validateLoginRequest($data);
    }

    public function testMissingEmail(): void
    {
        $data = [
            'password' => 'validpassword',
        ];

        $this->expectException(ValidatorException::class);
        UserLoginValidator::validateLoginRequest($data);
    }

    public function testMissingPassword(): void
    {
        $data = [
            'email' => '<EMAIL>',
        ];

        $this->expectException(ValidatorException::class);
        UserLoginValidator::validateLoginRequest($data);
    }

    public function testEmptyEmail(): void
    {
        $data = [
            'email' => '',
            'password' => 'validpassword',
        ];

        $this->expectException(ValidatorException::class);
        UserLoginValidator::validateLoginRequest($data);
    }

    public function testEmptyPassword(): void
    {
        $data = [
            'email' => '<EMAIL>',
            'password' => '',
        ];

        $this->expectException(ValidatorException::class);
        UserLoginValidator::validateLoginRequest($data);
    }
}

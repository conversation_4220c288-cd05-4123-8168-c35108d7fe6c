<?php

declare(strict_types=1);

namespace App\Tests\Functional\Auth;

use App\Domain\Shared\Email\Email;
use App\Domain\Shared\Email\InvalidEmailException;
use App\Domain\Shared\Uuid\InvalidUuidException;
use App\Domain\User\UserRole;
use App\Tests\Functional\Fixtures\UserFixtures;
use App\Tests\Functional\FunctionalTestCase;
use Symfony\Component\HttpFoundation\Response;

class TokenRefreshFunctionalTest extends FunctionalTestCase
{
    use UserFixtures;

    /**
     * @throws InvalidUuidException
     * @throws InvalidEmailException
     */
    public function testSuccessfulTokenRefresh(): void
    {
        // 1. First, login to get a refresh token
        $email = new Email('<EMAIL>');
        $password = 'password123';
        $passwordHash = password_hash($password, PASSWORD_DEFAULT);

        $this->setAndGetUserInRepository(
            email: $email,
            role: UserRole::User,
            passwordHash: $passwordHash
        );

        $loginResponse = $this->makeRequest(
            method: 'POST',
            uri: '/auth/login',
            body: [
                'email' => $email->value(),
                'password' => $password,
            ]
        );

        $this->assertEquals(Response::HTTP_OK, $loginResponse->getStatusCode());
        
        $loginContent = json_decode($loginResponse->getContent(), true);
        $this->assertArrayHasKey('refresh_token', $loginContent);
        $refreshToken = $loginContent['refresh_token'];

        // 2. Now use the refresh token to get new tokens
        $refreshResponse = $this->makeRequest(
            method: 'POST',
            uri: '/auth/refresh',
            body: [
                'refresh_token' => $refreshToken,
            ]
        );

        $this->assertEquals(Response::HTTP_OK, $refreshResponse->getStatusCode());
        
        $refreshContent = json_decode($refreshResponse->getContent(), true);
        $this->assertArrayHasKey('token', $refreshContent);
        $this->assertArrayHasKey('refresh_token', $refreshContent);
        $this->assertNotEmpty($refreshContent['token']);
        $this->assertNotEmpty($refreshContent['refresh_token']);
    }

    public function testInvalidRefreshToken(): void
    {
        $response = $this->makeRequest(
            method: 'POST',
            uri: '/auth/refresh',
            body: [
                'refresh_token' => 'invalid.refresh.token',
            ]
        );

        $this->assertEquals(Response::HTTP_UNAUTHORIZED, $response->getStatusCode());
        
        $content = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('error', $content);
        $this->assertStringContainsString('Invalid refresh token', $content['error']);
    }

    public function testMissingRefreshToken(): void
    {
        $response = $this->makeRequest(
            method: 'POST',
            uri: '/auth/refresh',
            body: []
        );

        $this->assertEquals(Response::HTTP_BAD_REQUEST, $response->getStatusCode());
        
        $content = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('error', $content);
    }
}
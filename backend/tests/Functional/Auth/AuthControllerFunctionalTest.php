<?php

declare(strict_types=1);

namespace App\Tests\Functional\Auth;

use App\Domain\Shared\Email\Email;
use App\Domain\Shared\Email\InvalidEmailException;
use App\Domain\Shared\Uuid\InvalidUuidException;
use App\Domain\User\UserRole;
use App\Tests\Functional\Fixtures\UserFixtures;
use App\Tests\Functional\FunctionalTestCase;
use Symfony\Component\HttpFoundation\Response;

class AuthControllerFunctionalTest extends FunctionalTestCase
{
    use UserFixtures;

    /**
     * @throws InvalidUuidException
     * @throws InvalidEmailException
     */
    public function testSuccessfulLogin(): void
    {
        $email = new Email('<EMAIL>');
        $password = 'password123';
        $passwordHash = password_hash($password, PASSWORD_DEFAULT);

        $this->setAndGetUserInRepository(
            email: $email,
            role: UserRole::User,
            passwordHash: $passwordHash
        );

        $response = $this->makeRequest(
            method: 'POST',
            uri: '/auth/login',
            body: [
                'email' => $email->value(),
                'password' => $password,
            ]
        );

        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());

        $content = json_decode($response->getContent(), true);
        $this->assertIsArray($content);
        $this->assertArrayHasKey('token', $content);
        $this->assertNotEmpty($content['token']);
        $this->assertArrayHasKey('refresh_token', $content);
        $this->assertNotEmpty($content['refresh_token']);
    }

    public function testInvalidCredentials(): void
    {
        $response = $this->makeRequest(
            method: 'POST',
            uri: '/auth/login',
            body: [
                'email' => '<EMAIL>',
                'password' => 'wrongpassword',
            ]
        );

        $this->assertEquals(Response::HTTP_UNAUTHORIZED, $response->getStatusCode());

        $content = json_decode($response->getContent(), true);
        $this->assertIsArray($content);
        $this->assertArrayHasKey('error', $content);
        $this->assertEquals('Invalid credentials', $content['error']);
    }

    public function testInvalidRequestFormat(): void
    {
        $response = $this->makeRequest(
            method: 'POST',
            uri: '/auth/login',
            body: [
                'email' => '<EMAIL>',
            ]
        );

        $this->assertEquals(Response::HTTP_BAD_REQUEST, $response->getStatusCode());

        $content = json_decode($response->getContent(), true);
        $this->assertIsArray($content);
        $this->assertArrayHasKey('error', $content);
        $this->assertEquals('Invalid credentials format', $content['error']);
    }
}

# This file is the entry point to configure your own services.
# Files in the packages/ subdirectory configure your dependencies.

# Put parameters here that don't need to change on each machine where the app is deployed
# https://symfony.com/doc/current/best_practices.html#use-parameters-for-application-configuration
parameters:

services:
    # default configuration for services in *this* file
    _defaults:
        autowire: true      # Automatically injects dependencies in your services.
        autoconfigure: true # Automatically registers your services as commands, event subscribers, etc.
        bind:
            $logger: '@logger'
            # Table names for the database
            string $userTableName: 'user'
            string $clientTableName: 'client'

            # Log
            string $logPath: "%kernel.logs_dir%"

    # makes classes in src/ available to be used as services
    # this creates a service per class whose id is the fully-qualified class name
    App\:
        resource: '../src/'
        exclude:
            - '../src/DependencyInjection/'
            - '../src/Entity/'
            - '../src/Kernel.php'

    # add more service definitions when explicit configuration is needed
    # please note that last definitions always *replace* previous ones

    App\Infrastructure\Controller\:
        resource: '../src/Infrastructure/Controller'
        tags: [ 'controller.service_arguments' ]

    App\Application\CommandHandler\:
        resource: '../src/Application/CommandHandler/'
        tags:
            - { name: 'tactician.handler', typehints: true }

    App\Application\QueryHandler\:
        resource: '../src/Application/QueryHandler/'
        tags:
            - { name: 'tactician.handler', typehints: true }

    App\Application\Log\Logger:
        alias: 'App\Infrastructure\Log\MonologLogger'


    # Repositories
    App\Domain\User\UserRepository:
        alias: App\Infrastructure\User\DBALUserRepository

    # Listeners
    App\Infrastructure\Listener\CorsListener:
        tags:
            - { name: kernel.event_subscriber }

    # Security
    App\Infrastructure\Security\Token\KeyProvider:
        arguments:
            $publicKeyPath: '%kernel.project_dir%/config/jwt/public.pem'
            $privateKeyPath: '%kernel.project_dir%/config/jwt/private.pem'

    App\Infrastructure\Security\Authentication\AuthenticationService:
        arguments:
            $tokenExpiration: '+1 hour'
            $refreshTokenExpiration: '+30 days'

    App\Infrastructure\Security\Authentication\TokenRefreshService:
        arguments:
            $tokenExpiration: '+1 hour'
            $refreshTokenExpiration: '+30 days'
